"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FAQItem } from "./types";

const FAQSection: React.FC<{ faqs: FAQItem[] }> = ({ faqs }) => {
  const [openFAQ, setOpenFAQ] = useState<string | null>(null);

  const toggleFAQ = (id: string) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <div className="space-y-4">
      {faqs.map((faq) => (
        <motion.div
          key={faq.id}
          className="faq-item bg-white/5 rounded-xl overflow-hidden"
          layout
        >
          <button
            onClick={() => toggleFAQ(faq.id)}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-white/10 transition-all"
          >
            <span className="text-white font-medium">{faq.question}</span>
            <motion.i
              className={`fas fa-chevron-down text-gray-400`}
              animate={{ rotate: openFAQ === faq.id ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            ></motion.i>
          </button>
          <AnimatePresence>
            {openFAQ === faq.id && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="px-4 pb-4"
              >
                <div className="text-gray-300">{faq.answer}</div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      ))}
    </div>
  );
};

export default FAQSection;
