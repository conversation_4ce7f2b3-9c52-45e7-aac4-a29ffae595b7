"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { SearchSuggestion } from "./types";

const SearchBar: React.FC<{
  onSearch: (query: string) => void;
  suggestions: SearchSuggestion[];
}> = ({ onSearch, suggestions }) => {
  const [query, setQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<
    SearchSuggestion[]
  >([]);

  useEffect(() => {
    if (query.length > 2) {
      const filtered = suggestions.filter((s) =>
        s.text.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredSuggestions(filtered);
    } else {
      setFilteredSuggestions(suggestions.slice(0, 4)); // Show popular searches
    }
  }, [query, suggestions]);

  const handleSearch = () => {
    onSearch(query);
    setShowSuggestions(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <div className="max-w-2xl mx-auto relative">
      <div className="relative">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setShowSuggestions(true)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
          onKeyPress={handleKeyPress}
          placeholder="Search for help articles, API docs, tutorials..."
          className="w-full bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl px-6 py-4 pl-14 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#0066CC] focus:border-transparent text-lg shadow-[0_0_20px_rgba(0,102,204,0.3)]"
        />
        <i className="fas fa-search absolute left-5 top-5 text-gray-400 text-xl"></i>
        <button
          onClick={handleSearch}
          className="absolute right-3 top-3 bg-[#0066CC] text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-all"
        >
          Search
        </button>
      </div>

      <AnimatePresence>
        {showSuggestions && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute w-full mt-2 bg-white rounded-xl shadow-lg z-10"
          >
            <div className="p-4">
              <div className="text-sm text-gray-500 mb-2">
                {query.length > 2 ? "Search results:" : "Popular searches:"}
              </div>
              <div className="flex flex-wrap gap-2">
                {filteredSuggestions.map((suggestion) => (
                  <span
                    key={suggestion.id}
                    onClick={() => {
                      setQuery(suggestion.text);
                      onSearch(suggestion.text);
                      setShowSuggestions(false);
                    }}
                    className="px-3 py-1 bg-gray-100 rounded-full text-sm cursor-pointer hover:bg-gray-200 transition-colors"
                  >
                    {suggestion.text}
                  </span>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SearchBar;
