"use client";

import React from "react";
import { HelpCategory } from "./types";

const CategoryNavigation: React.FC<{
  categories: HelpCategory[];
  activeSection: string;
  onSectionClick: (sectionId: string) => void;
}> = ({ categories, activeSection, onSectionClick }) => {
  return (
    <div className="category-nav bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-6 sticky top-24">
      <h3 className="text-lg font-bold text-white mb-6">Categories</h3>

      <nav className="space-y-2">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => onSectionClick(category.id)}
            className={`w-full text-left px-4 py-3 rounded-lg transition-all ${
              activeSection === category.id
                ? "bg-white/20 text-white"
                : "text-gray-300 hover:text-white hover:bg-white/10"
            }`}
          >
            <i className={`fas ${category.icon} mr-3 ${category.color}`}></i>
            {category.name}
          </button>
        ))}
      </nav>

      <div className="mt-8 pt-6 border-t border-white/10">
        <h4 className="text-sm font-semibold text-white mb-4">Quick Links</h4>
        <div className="space-y-2 text-sm">
          <a
            href="#"
            className="text-[#0066CC] hover:text-[#14B8A6] transition-colors block"
          >
            <i className="fas fa-file-pdf mr-2"></i>API Reference PDF
          </a>
          <a
            href="#"
            className="text-[#0066CC] hover:text-[#14B8A6] transition-colors block"
          >
            <i className="fas fa-video mr-2"></i>Video Tutorials
          </a>
          <a
            href="#"
            className="text-[#0066CC] hover:text-[#14B8A6] transition-colors block"
          >
            <i className="fas fa-users mr-2"></i>Community Forum
          </a>
          <a
            href="#"
            className="text-[#0066CC] hover:text-[#14B8A6] transition-colors block"
          >
            <i className="fas fa-calendar mr-2"></i>Schedule Demo
          </a>
        </div>
      </div>
    </div>
  );
};

export default CategoryNavigation;
