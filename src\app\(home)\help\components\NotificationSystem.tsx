"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface Notification {
  id: string;
  message: string;
  type: "success" | "error" | "info" | "warning";
  duration?: number;
}

const NotificationSystem: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const addNotification = (notification: Omit<Notification, "id">) => {
    const id = Date.now().toString();
    const newNotification = { ...notification, id };
    setNotifications((prev) => [...prev, newNotification]);

    setTimeout(() => {
      removeNotification(id);
    }, notification.duration || 5000);
  };

  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
  };

  // Expose addNotification globally for other components
  //   useEffect(() => {
  //     (window as any).showNotification = addNotification;
  //   }, []);
  console.warn(addNotification);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return "fa-check-circle";
      case "error":
        return "fa-times-circle";
      case "warning":
        return "fa-exclamation-circle";
      default:
        return "fa-info-circle";
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case "success":
        return "bg-[#10B981]";
      case "error":
        return "bg-[#EF4444]";
      case "warning":
        return "bg-[#F59E0B]";
      default:
        return "bg-[#0066CC]";
    }
  };

  return (
    <div className="fixed top-20 right-6 z-50 space-y-2">
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className={`p-4 rounded-xl shadow-lg text-white ${getNotificationColor(
              notification.type
            )}`}
          >
            <div className="flex items-center space-x-3">
              <i
                className={`fas ${getNotificationIcon(notification.type)}`}
              ></i>
              <span>{notification.message}</span>
              <button
                onClick={() => removeNotification(notification.id)}
                className="ml-4 hover:bg-white/20 rounded p-1 transition-colors"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default NotificationSystem;
