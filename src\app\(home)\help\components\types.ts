export interface FAQItem {
  id: string;
  question: string;
  answer: React.ReactNode;
  category: string;
}

export interface SupportChannel {
  id: string;
  name: string;
  description: string;
  responseTime: string;
  icon: string;
  color: string;
  action: () => void;
  available?: boolean;
}

export interface HelpCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  articleCount: number;
}

export interface SearchSuggestion {
  id: string;
  text: string;
  category: string;
}
