"use client";

import React, { useState, useEffect, useMemo } from "react";
import Head from "next/head";
import { motion } from "framer-motion";
import SearchBar from "./components/SearchBar";
import NotificationSystem from "./components/NotificationSystem";
import FAQSection from "./components/FAQSection";
import CodeExample from "./components/CodeExample";
import SupportChannels from "./components/SupportChannels";
import CategoryNavigation from "./components/CategoryNavigation";
import LiveChatWidget from "./components/LiveChatWidget";
// import { toast } from 'sonner';

// Types and Interfaces
interface FAQItem {
  id: string;
  question: string;
  answer: React.ReactNode;
  category: string;
}

interface SupportChannel {
  id: string;
  name: string;
  description: string;
  responseTime: string;
  icon: string;
  color: string;
  action: () => void;
  available?: boolean;
}

interface HelpCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  articleCount: number;
}

interface Notification {
  id: string;
  message: string;
  type: "success" | "error" | "info" | "warning";
  duration?: number;
}

interface SearchSuggestion {
  id: string;
  text: string;
  category: string;
}

const SearchBar: React.FC<{
  onSearch: (query: string) => void;
  suggestions: SearchSuggestion[];
}> = ({ onSearch, suggestions }) => {
  const [query, setQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<
    SearchSuggestion[]
  >([]);

  useEffect(() => {
    if (query.length > 2) {
      const filtered = suggestions.filter((s) =>
        s.text.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredSuggestions(filtered);
    } else {
      setFilteredSuggestions(suggestions.slice(0, 4)); // Show popular searches
    }
  }, [query, suggestions]);

  const handleSearch = () => {
    onSearch(query);
    setShowSuggestions(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <div className="max-w-2xl mx-auto relative">
      <div className="relative">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setShowSuggestions(true)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
          onKeyPress={handleKeyPress}
          placeholder="Search for help articles, API docs, tutorials..."
          className="w-full bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl px-6 py-4 pl-14 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#0066CC] focus:border-transparent text-lg shadow-[0_0_20px_rgba(0,102,204,0.3)]"
        />
        <i className="fas fa-search absolute left-5 top-5 text-gray-400 text-xl"></i>
        <button
          onClick={handleSearch}
          className="absolute right-3 top-3 bg-[#0066CC] text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-all"
        >
          Search
        </button>
      </div>

      <AnimatePresence>
        {showSuggestions && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute w-full mt-2 bg-white rounded-xl shadow-lg z-10"
          >
            <div className="p-4">
              <div className="text-sm text-gray-500 mb-2">
                {query.length > 2 ? "Search results:" : "Popular searches:"}
              </div>
              <div className="flex flex-wrap gap-2">
                {filteredSuggestions.map((suggestion) => (
                  <span
                    key={suggestion.id}
                    onClick={() => {
                      setQuery(suggestion.text);
                      onSearch(suggestion.text);
                      setShowSuggestions(false);
                    }}
                    className="px-3 py-1 bg-gray-100 rounded-full text-sm cursor-pointer hover:bg-gray-200 transition-colors"
                  >
                    {suggestion.text}
                  </span>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const NotificationSystem: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const addNotification = (notification: Omit<Notification, "id">) => {
    const id = Date.now().toString();
    const newNotification = { ...notification, id };
    setNotifications((prev) => [...prev, newNotification]);

    setTimeout(() => {
      removeNotification(id);
    }, notification.duration || 5000);
  };

  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
  };

  // Expose addNotification globally for other components
  //   useEffect(() => {
  //     (window as any).showNotification = addNotification;
  //   }, []);
  console.warn(addNotification);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return "fa-check-circle";
      case "error":
        return "fa-times-circle";
      case "warning":
        return "fa-exclamation-circle";
      default:
        return "fa-info-circle";
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case "success":
        return "bg-[#10B981]";
      case "error":
        return "bg-[#EF4444]";
      case "warning":
        return "bg-[#F59E0B]";
      default:
        return "bg-[#0066CC]";
    }
  };

  return (
    <div className="fixed top-20 right-6 z-50 space-y-2">
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className={`p-4 rounded-xl shadow-lg text-white ${getNotificationColor(
              notification.type
            )}`}
          >
            <div className="flex items-center space-x-3">
              <i
                className={`fas ${getNotificationIcon(notification.type)}`}
              ></i>
              <span>{notification.message}</span>
              <button
                onClick={() => removeNotification(notification.id)}
                className="ml-4 hover:bg-white/20 rounded p-1 transition-colors"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

const FAQSection: React.FC<{ faqs: FAQItem[] }> = ({ faqs }) => {
  const [openFAQ, setOpenFAQ] = useState<string | null>(null);

  const toggleFAQ = (id: string) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <div className="space-y-4">
      {faqs.map((faq) => (
        <motion.div
          key={faq.id}
          className="faq-item bg-white/5 rounded-xl overflow-hidden"
          layout
        >
          <button
            onClick={() => toggleFAQ(faq.id)}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-white/10 transition-all"
          >
            <span className="text-white font-medium">{faq.question}</span>
            <motion.i
              className={`fas fa-chevron-down text-gray-400`}
              animate={{ rotate: openFAQ === faq.id ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            ></motion.i>
          </button>
          <AnimatePresence>
            {openFAQ === faq.id && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="px-4 pb-4"
              >
                <div className="text-gray-300">{faq.answer}</div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      ))}
    </div>
  );
};

const CodeExample: React.FC<{ code: string; language: string }> = ({
  code,
  language,
}) => {
  const [copied, setCopied] = useState(false);
  console.warn(language);
  const copyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      toast.success("Code copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.warn(err);
      //   toast.error('Failed to copy code');
    }
  };

  return (
    <div className="bg-gray-900 rounded-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">
          Example: Climate Risk Assessment
        </h3>
        <button
          onClick={copyCode}
          className="text-[#0066CC] hover:text-[#14B8A6] transition-colors text-sm flex items-center space-x-2"
        >
          <i className={`fas ${copied ? "fa-check" : "fa-copy"}`}></i>
          <span>{copied ? "Copied!" : "Copy Code"}</span>
        </button>
      </div>
      <pre className="text-gray-300 text-sm overflow-x-auto">
        <code>{code}</code>
      </pre>
    </div>
  );
};

const SupportChannels: React.FC<{ channels: SupportChannel[] }> = ({
  channels,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {channels.map((channel) => (
        <motion.div
          key={channel.id}
          whileHover={{ y: -4 }}
          className="bg-white/5 rounded-xl p-6 text-center cursor-pointer"
          onClick={channel.action}
        >
          <div
            className={`w-12 h-12 ${channel.color} rounded-xl flex items-center justify-center mx-auto mb-4`}
          >
            <i className={`fas ${channel.icon} text-white`}></i>
          </div>
          <h4 className="text-lg font-semibold text-white mb-2">
            {channel.name}
          </h4>
          <p className="text-gray-300 text-sm mb-4">{channel.description}</p>
          <div className="text-xs text-gray-400 mb-3">
            Response time: {channel.responseTime}
          </div>
          <button
            className={`${channel.color} text-white px-4 py-2 rounded-lg hover:opacity-80 transition-all text-sm font-medium`}
          >
            {channel.name === "Live Chat"
              ? "Start Chat"
              : channel.name === "Email Support"
              ? "Send Email"
              : "+1 (555) 123-4567"}
          </button>
        </motion.div>
      ))}
    </div>
  );
};

const CategoryNavigation: React.FC<{
  categories: HelpCategory[];
  activeSection: string;
  onSectionClick: (sectionId: string) => void;
}> = ({ categories, activeSection, onSectionClick }) => {
  return (
    <div className="category-nav bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-6 sticky top-24">
      <h3 className="text-lg font-bold text-white mb-6">Categories</h3>

      <nav className="space-y-2">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => onSectionClick(category.id)}
            className={`w-full text-left px-4 py-3 rounded-lg transition-all ${
              activeSection === category.id
                ? "bg-white/20 text-white"
                : "text-gray-300 hover:text-white hover:bg-white/10"
            }`}
          >
            <i className={`fas ${category.icon} mr-3 ${category.color}`}></i>
            {category.name}
          </button>
        ))}
      </nav>

      <div className="mt-8 pt-6 border-t border-white/10">
        <h4 className="text-sm font-semibold text-white mb-4">Quick Links</h4>
        <div className="space-y-2 text-sm">
          <a
            href="#"
            className="text-[#0066CC] hover:text-[#14B8A6] transition-colors block"
          >
            <i className="fas fa-file-pdf mr-2"></i>API Reference PDF
          </a>
          <a
            href="#"
            className="text-[#0066CC] hover:text-[#14B8A6] transition-colors block"
          >
            <i className="fas fa-video mr-2"></i>Video Tutorials
          </a>
          <a
            href="#"
            className="text-[#0066CC] hover:text-[#14B8A6] transition-colors block"
          >
            <i className="fas fa-users mr-2"></i>Community Forum
          </a>
          <a
            href="#"
            className="text-[#0066CC] hover:text-[#14B8A6] transition-colors block"
          >
            <i className="fas fa-calendar mr-2"></i>Schedule Demo
          </a>
        </div>
      </div>
    </div>
  );
};

const LiveChatWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const handleChatClick = () => {
    setIsOpen(!isOpen);
    // if (!isOpen) {
    // toast('Connecting to support agent...', {
    //   description: 'Please wait a moment.',
    //   duration: 2000,
    // });

    // setTimeout(() => {
    //   toast.success('Connected!', {
    //     description: 'A support agent will help you shortly.',
    //   });
    // }, 2000);
    // }
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        onClick={handleChatClick}
        className="bg-[#10B981] text-white p-4 rounded-full shadow-lg hover:bg-green-600 transition-all"
      >
        <i className="fas fa-comments text-xl"></i>
      </motion.button>
    </div>
  );
};

// Main Help Page Component
const HelpPage: React.FC = () => {
  const [activeSection, setActiveSection] = useState("getting-started");
  const [searchQuery, setSearchQuery] = useState("");
  console.warn(searchQuery);
  // Data
  const searchSuggestions: SearchSuggestion[] = [
    { id: "1", text: "API documentation", category: "development" },
    { id: "2", text: "Risk assessment", category: "platform" },
    { id: "3", text: "Billing", category: "account" },
    { id: "4", text: "Data export", category: "platform" },
    { id: "5", text: "Portfolio management", category: "platform" },
    { id: "6", text: "AI insights", category: "features" },
  ];

  const helpCategories: HelpCategory[] = useMemo(
    () => [
      {
        id: "getting-started",
        name: "Getting Started",
        description: "Learn the basics and set up your first assessment",
        icon: "fa-rocket",
        color: "text-[#10B981]",
        articleCount: 15,
      },
      {
        id: "api-docs",
        name: "API Documentation",
        description: "Integrate ClimateForte into your applications",
        icon: "fa-code",
        color: "text-[#0066CC]",
        articleCount: 32,
      },
      {
        id: "features",
        name: "Platform Features",
        description: "Explore advanced analytics and insights",
        icon: "fa-tools",
        color: "text-[#8B5CF6]",
        articleCount: 28,
      },
      {
        id: "billing",
        name: "Account & Billing",
        description: "Manage subscriptions and payment settings",
        icon: "fa-credit-card",
        color: "text-[#F59E0B]",
        articleCount: 12,
      },
      {
        id: "troubleshooting",
        name: "Troubleshooting",
        description: "Common issues and solutions",
        icon: "fa-wrench",
        color: "text-[#EF4444]",
        articleCount: 18,
      },
      {
        id: "security",
        name: "Security & Privacy",
        description: "Data protection and compliance",
        icon: "fa-shield-alt",
        color: "text-[#14B8A6]",
        articleCount: 8,
      },
    ],
    []
  );

  const faqs: FAQItem[] = [
    {
      id: "faq1",
      question: "How do I perform my first climate risk assessment?",
      category: "getting-started",
      answer: (
        <div className="space-y-3">
          <p>Getting started with ClimateForte is simple:</p>
          <ol className="list-decimal list-inside space-y-2 ml-4">
            <li>
              <strong>Enter a location:</strong> Use our search to find any
              address worldwide
            </li>
            <li>
              <strong>Select your industry:</strong> Choose from Real Estate,
              Agriculture, Energy, Manufacturing, etc.
            </li>
            <li>
              <strong>Set time horizon:</strong> Pick your assessment period
              (5-50 years)
            </li>
            <li>
              <strong>Review results:</strong> Analyze your comprehensive risk
              report
            </li>
          </ol>
          <div className="bg-[#0066CC]/20 border border-[#0066CC]/30 rounded-lg p-3 mt-4">
            <p className="text-sm">
              <i className="fas fa-lightbulb mr-2 text-[#0066CC]"></i>
              <strong>Pro Tip:</strong> Start with a location you are familiar
              with to understand how our risk scoring works.
            </p>
          </div>
        </div>
      ),
    },
    {
      id: "faq2",
      question: "What do the risk scores mean?",
      category: "getting-started",
      answer: (
        <div className="space-y-3">
          <p>
            Our risk scores range from 0.0 to 1.0 (0% to 100%) and represent:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="bg-[#10B981]/20 border border-[#10B981]/30 rounded-lg p-3">
              <div className="text-[#10B981] font-semibold">
                0.0 - 0.3 (Low Risk)
              </div>
              <div className="text-sm">Minimal climate impact expected</div>
            </div>
            <div className="bg-[#F59E0B]/20 border border-[#F59E0B]/30 rounded-lg p-3">
              <div className="text-[#F59E0B] font-semibold">
                0.3 - 0.7 (Medium Risk)
              </div>
              <div className="text-sm">
                Moderate climate impact, monitoring advised
              </div>
            </div>
            <div className="bg-[#EF4444]/20 border border-[#EF4444]/30 rounded-lg p-3">
              <div className="text-[#EF4444] font-semibold">
                0.7 - 1.0 (High Risk)
              </div>
              <div className="text-sm">
                Significant climate impact, action required
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "faq3",
      question: "How accurate are your climate projections?",
      category: "getting-started",
      answer: (
        <div className="space-y-3">
          <p>ClimateForte provides industry-leading accuracy through:</p>
          <ul className="list-disc list-inside space-y-2 ml-4">
            <li>
              <strong>95%+ Scientific Accuracy:</strong> Using IPCC AR6
              methodologies and peer-reviewed models
            </li>
            <li>
              <strong>Multiple Data Sources:</strong> NASA MERRA-2, NOAA Climate
              Data, CMIP6 projections
            </li>
            <li>
              <strong>Historical Validation:</strong> Tested against major
              climate events (Hurricane Katrina, California wildfires)
            </li>
            <li>
              <strong>Confidence Intervals:</strong> Every assessment includes
              statistical confidence levels
            </li>
          </ul>
          <div className="bg-gray-800 rounded-lg p-4 mt-4">
            <div className="text-sm text-gray-400 mb-2">
              Methodology Sources:
            </div>
            <div className="text-xs text-gray-500 space-y-1">
              <div>• Heat Risk: NOAA Rothfusz Heat Index (1990)</div>
              <div>• Wildfire: Canadian Fire Weather Index System</div>
              <div>• Flood: USDA NRCS Technical Release 55</div>
              <div>• Sea Level: IPCC AR6 Regional Projections</div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  const supportChannels: SupportChannel[] = [
    {
      id: "chat",
      name: "Live Chat",
      description: "Instant support during business hours",
      responseTime: "~2 minutes",
      icon: "fa-comments",
      color: "bg-[#10B981]",
      available: true,
      action: () => {
        //  toast.info("Connecting to support agent...", {
        //    description: 'Please wait a moment.',
        //    duration: 3000,
        //  });
      },
    },
    {
      id: "email",
      name: "Email Support",
      description: "Detailed help via email",
      responseTime: "~4 hours",
      icon: "fa-envelope",
      color: "bg-[#0066CC]",
      available: true,
      action: () => {
        window.location.href =
          "mailto:<EMAIL>?subject=Support Request&body=Please describe your issue:";
      },
    },
    {
      id: "phone",
      name: "Phone Support",
      description: "Direct line to our experts",
      responseTime: "Enterprise customers only",
      icon: "fa-phone",
      color: "bg-[#8B5CF6]",
      available: true,
      action: () => {
        //    toast.info("Phone support available for Enterprise customers", {
        //      description: "Upgrade to Enterprise to access this feature.",
        //      duration: 4000,
        //    });
      },
    },
  ];

  const codeExample = `// JavaScript/Node.js Example
const response = await fetch('https://api.climateforte.com/climate/assess', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    latitude: 40.7128,
    longitude: -74.0060,
    industry: 'real_estate',
    timeHorizon: 30,
    options: {
      includeHistorical: true,
      includeProjections: true,
      detailedReport: true
    }
  })
});

const assessment = await response.json();

console.log('Overall Risk Score:', assessment.overallRisk);
console.log('Risk Categories:', assessment.categoryRisks);
console.log('Business Impact:', assessment.businessImpact);
console.log('Confidence Level:', assessment.confidence);`;

  // Event Handlers
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    //    toast.info(`Searching for: "${query}"`);
  };

  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  // Initialize notifications on mount
  //   useEffect(() => {
  //     setTimeout(() => {
  //       toast.success("Welcome to ClimateForte Help Center! How can we assist you?");
  //     }, 1000);
  //   }, []);

  // Scroll spy for active section
  useEffect(() => {
    const handleScroll = () => {
      const sections = helpCategories.map((cat) => cat.id);
      const scrollPosition = window.scrollY + 200;

      for (const sectionId of sections) {
        const element = document.getElementById(sectionId);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (
            scrollPosition >= offsetTop &&
            scrollPosition < offsetTop + offsetHeight
          ) {
            setActiveSection(sectionId);
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [helpCategories]);

  return (
    <>
      <Head>
        <title>Help & Support - ClimateForte</title>
        <meta
          name="description"
          content="Get help with ClimateForte's climate risk intelligence platform. Find answers, API docs, and expert support."
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        <link
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
          rel="stylesheet"
        />
      </Head>

      <div className="bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 min-h-screen">
        {/* <Navbar /> */}
        <NotificationSystem />

        {/* Hero Section */}
        <div className="max-w-7xl mx-auto px-6 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              How can we help you today?
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Find answers, learn about our platform, and get expert support for
              your climate risk intelligence needs.
            </p>

            <SearchBar
              onSearch={handleSearch}
              suggestions={searchSuggestions}
            />
          </motion.div>

          {/* Quick Help Categories */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
          >
            {helpCategories.slice(0, 4).map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
                onClick={() => scrollToSection(category.id)}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-6 cursor-pointer"
              >
                <div
                  className={`w-16 h-16 ${category.color.replace(
                    "text-",
                    "bg-"
                  )} rounded-2xl flex items-center justify-center mb-4 mx-auto`}
                >
                  <i className={`fas ${category.icon} text-white text-2xl`}></i>
                </div>
                <h3 className="text-xl font-bold text-white text-center mb-2">
                  {category.name}
                </h3>
                <p className="text-gray-300 text-center text-sm">
                  {category.description}
                </p>
                <div className="text-center mt-4">
                  <span className={`${category.color} text-sm font-medium`}>
                    {category.articleCount} articles
                  </span>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Contact Support Options */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-8 mb-12"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-white mb-4">
                Need Personalized Support?
              </h3>
              <p className="text-gray-300">
                Our expert team is here to help you succeed with climate risk
                intelligence.
              </p>
            </div>

            <SupportChannels channels={supportChannels} />
          </motion.div>
        </div>

        {/* Main Help Content */}
        <div className="max-w-7xl mx-auto px-6 pb-12">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar Navigation */}
            <div className="lg:col-span-1">
              <CategoryNavigation
                categories={helpCategories}
                activeSection={activeSection}
                onSectionClick={scrollToSection}
              />
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3 space-y-12">
              {/* Getting Started Section */}
              <motion.section
                id="getting-started"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-8"
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-[#10B981] rounded-xl flex items-center justify-center mr-4">
                    <i className="fas fa-rocket text-white"></i>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">
                      Getting Started
                    </h2>
                    <p className="text-gray-300">
                      Essential guides to help you begin your climate risk
                      assessment journey
                    </p>
                  </div>
                </div>

                <FAQSection faqs={faqs} />
              </motion.section>

              {/* API Documentation Section */}
              <motion.section
                id="api-docs"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-8"
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-[#0066CC] rounded-xl flex items-center justify-center mr-4">
                    <i className="fas fa-code text-white"></i>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">
                      API Documentation
                    </h2>
                    <p className="text-gray-300">
                      Integrate ClimateForte&#39;s climate intelligence into
                      your applications
                    </p>
                  </div>
                </div>

                {/* API Overview */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-white/5 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">
                      <i className="fas fa-bolt text-[#0066CC] mr-2"></i>Quick
                      Start
                    </h3>
                    <div className="space-y-3 text-sm text-gray-300">
                      <div>1. Get your API key from Settings</div>
                      <div>2. Make your first assessment call</div>
                      <div>3. Parse the JSON response</div>
                      <div>4. Integrate into your workflow</div>
                    </div>
                    <button className="mt-4 bg-[#0066CC] text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-all">
                      View Quick Start Guide
                    </button>
                  </div>

                  <div className="bg-white/5 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">
                      <i className="fas fa-book text-[#10B981] mr-2"></i>API
                      Reference
                    </h3>
                    <div className="space-y-2 text-sm text-gray-300">
                      <div>
                        <span className="text-[#10B981]">GET</span>{" "}
                        /climate/assess - Risk assessment
                      </div>
                      <div>
                        <span className="text-[#0066CC]">POST</span>{" "}
                        /portfolio/bulk - Bulk analysis
                      </div>
                      <div>
                        <span className="text-[#8B5CF6]">GET</span> /insights/ai
                        - AI predictions
                      </div>
                      <div>
                        <span className="text-[#F59E0B]">GET</span>{" "}
                        /weather/current - Live weather
                      </div>
                    </div>
                    <button className="mt-4 bg-[#10B981] text-white px-4 py-2 rounded-lg text-sm hover:bg-green-700 transition-all">
                      Full API Reference
                    </button>
                  </div>
                </div>

                <CodeExample code={codeExample} language="javascript" />

                {/* API Status */}
                <div className="bg-white/5 rounded-xl p-4 mt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative inline-block w-3 h-3 rounded-full bg-green-500 after:content-[''] after:absolute after:top-0 after:left-0 after:w-full after:h-full after:rounded-full"></div>
                      <span className="text-white font-medium">
                        API Status: All Systems Operational
                      </span>
                    </div>
                    <div className="text-sm text-gray-400">
                      Uptime: 99.97% | Response Time: 180ms
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Additional sections would follow the same pattern... */}
              {/* For brevity, I'm showing the structure for the remaining sections */}

              <motion.section
                id="features"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-8"
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-[#8B5CF6] rounded-xl flex items-center justify-center mr-4">
                    <i className="fas fa-tools text-white"></i>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">
                      Platform Features
                    </h2>
                    <p className="text-gray-300">
                      Master ClimateForte&#39;s powerful climate intelligence
                      capabilities
                    </p>
                  </div>
                </div>

                {/* Feature content here */}
                <div className="text-gray-300">
                  <p>Platform features content would be implemented here...</p>
                </div>
              </motion.section>

              {/* Continue with other sections... */}
            </div>
          </div>
        </div>

        <LiveChatWidget />
      </div>
    </>
  );
};

export default HelpPage;
