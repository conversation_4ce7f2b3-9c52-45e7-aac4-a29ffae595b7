"use client";

import React, { useState, useEffect, useMemo } from "react";
import Head from "next/head";
import { motion } from "framer-motion";
import SearchBar from "./components/SearchBar";
import NotificationSystem from "./components/NotificationSystem";
import FAQSection from "./components/FAQSection";
import CodeExample from "./components/CodeExample";
import SupportChannels from "./components/SupportChannels";
import CategoryNavigation from "./components/CategoryNavigation";
import LiveChatWidget from "./components/LiveChatWidget";
import {
  FAQItem,
  SupportChannel,
  HelpCategory,
  SearchSuggestion,
} from "./components/types";
// import { toast } from 'sonner';

// Main Help Page Component
const HelpPage: React.FC = () => {
  const [activeSection, setActiveSection] = useState("getting-started");
  const [searchQuery, setSearchQuery] = useState("");
  console.warn(searchQuery);
  // Data
  const searchSuggestions: SearchSuggestion[] = [
    { id: "1", text: "API documentation", category: "development" },
    { id: "2", text: "Risk assessment", category: "platform" },
    { id: "3", text: "Billing", category: "account" },
    { id: "4", text: "Data export", category: "platform" },
    { id: "5", text: "Portfolio management", category: "platform" },
    { id: "6", text: "AI insights", category: "features" },
  ];

  const helpCategories: HelpCategory[] = useMemo(
    () => [
      {
        id: "getting-started",
        name: "Getting Started",
        description: "Learn the basics and set up your first assessment",
        icon: "fa-rocket",
        color: "text-[#10B981]",
        articleCount: 15,
      },
      {
        id: "api-docs",
        name: "API Documentation",
        description: "Integrate ClimateForte into your applications",
        icon: "fa-code",
        color: "text-[#0066CC]",
        articleCount: 32,
      },
      {
        id: "features",
        name: "Platform Features",
        description: "Explore advanced analytics and insights",
        icon: "fa-tools",
        color: "text-[#8B5CF6]",
        articleCount: 28,
      },
      {
        id: "billing",
        name: "Account & Billing",
        description: "Manage subscriptions and payment settings",
        icon: "fa-credit-card",
        color: "text-[#F59E0B]",
        articleCount: 12,
      },
      {
        id: "troubleshooting",
        name: "Troubleshooting",
        description: "Common issues and solutions",
        icon: "fa-wrench",
        color: "text-[#EF4444]",
        articleCount: 18,
      },
      {
        id: "security",
        name: "Security & Privacy",
        description: "Data protection and compliance",
        icon: "fa-shield-alt",
        color: "text-[#14B8A6]",
        articleCount: 8,
      },
    ],
    []
  );

  const faqs: FAQItem[] = [
    {
      id: "faq1",
      question: "How do I perform my first climate risk assessment?",
      category: "getting-started",
      answer: (
        <div className="space-y-3">
          <p>Getting started with ClimateForte is simple:</p>
          <ol className="list-decimal list-inside space-y-2 ml-4">
            <li>
              <strong>Enter a location:</strong> Use our search to find any
              address worldwide
            </li>
            <li>
              <strong>Select your industry:</strong> Choose from Real Estate,
              Agriculture, Energy, Manufacturing, etc.
            </li>
            <li>
              <strong>Set time horizon:</strong> Pick your assessment period
              (5-50 years)
            </li>
            <li>
              <strong>Review results:</strong> Analyze your comprehensive risk
              report
            </li>
          </ol>
          <div className="bg-[#0066CC]/20 border border-[#0066CC]/30 rounded-lg p-3 mt-4">
            <p className="text-sm">
              <i className="fas fa-lightbulb mr-2 text-[#0066CC]"></i>
              <strong>Pro Tip:</strong> Start with a location you are familiar
              with to understand how our risk scoring works.
            </p>
          </div>
        </div>
      ),
    },
    {
      id: "faq2",
      question: "What do the risk scores mean?",
      category: "getting-started",
      answer: (
        <div className="space-y-3">
          <p>
            Our risk scores range from 0.0 to 1.0 (0% to 100%) and represent:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="bg-[#10B981]/20 border border-[#10B981]/30 rounded-lg p-3">
              <div className="text-[#10B981] font-semibold">
                0.0 - 0.3 (Low Risk)
              </div>
              <div className="text-sm">Minimal climate impact expected</div>
            </div>
            <div className="bg-[#F59E0B]/20 border border-[#F59E0B]/30 rounded-lg p-3">
              <div className="text-[#F59E0B] font-semibold">
                0.3 - 0.7 (Medium Risk)
              </div>
              <div className="text-sm">
                Moderate climate impact, monitoring advised
              </div>
            </div>
            <div className="bg-[#EF4444]/20 border border-[#EF4444]/30 rounded-lg p-3">
              <div className="text-[#EF4444] font-semibold">
                0.7 - 1.0 (High Risk)
              </div>
              <div className="text-sm">
                Significant climate impact, action required
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "faq3",
      question: "How accurate are your climate projections?",
      category: "getting-started",
      answer: (
        <div className="space-y-3">
          <p>ClimateForte provides industry-leading accuracy through:</p>
          <ul className="list-disc list-inside space-y-2 ml-4">
            <li>
              <strong>95%+ Scientific Accuracy:</strong> Using IPCC AR6
              methodologies and peer-reviewed models
            </li>
            <li>
              <strong>Multiple Data Sources:</strong> NASA MERRA-2, NOAA Climate
              Data, CMIP6 projections
            </li>
            <li>
              <strong>Historical Validation:</strong> Tested against major
              climate events (Hurricane Katrina, California wildfires)
            </li>
            <li>
              <strong>Confidence Intervals:</strong> Every assessment includes
              statistical confidence levels
            </li>
          </ul>
          <div className="bg-gray-800 rounded-lg p-4 mt-4">
            <div className="text-sm text-gray-400 mb-2">
              Methodology Sources:
            </div>
            <div className="text-xs text-gray-500 space-y-1">
              <div>• Heat Risk: NOAA Rothfusz Heat Index (1990)</div>
              <div>• Wildfire: Canadian Fire Weather Index System</div>
              <div>• Flood: USDA NRCS Technical Release 55</div>
              <div>• Sea Level: IPCC AR6 Regional Projections</div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  const supportChannels: SupportChannel[] = [
    {
      id: "chat",
      name: "Live Chat",
      description: "Instant support during business hours",
      responseTime: "~2 minutes",
      icon: "fa-comments",
      color: "bg-[#10B981]",
      available: true,
      action: () => {
        //  toast.info("Connecting to support agent...", {
        //    description: 'Please wait a moment.',
        //    duration: 3000,
        //  });
      },
    },
    {
      id: "email",
      name: "Email Support",
      description: "Detailed help via email",
      responseTime: "~4 hours",
      icon: "fa-envelope",
      color: "bg-[#0066CC]",
      available: true,
      action: () => {
        window.location.href =
          "mailto:<EMAIL>?subject=Support Request&body=Please describe your issue:";
      },
    },
    {
      id: "phone",
      name: "Phone Support",
      description: "Direct line to our experts",
      responseTime: "Enterprise customers only",
      icon: "fa-phone",
      color: "bg-[#8B5CF6]",
      available: true,
      action: () => {
        //    toast.info("Phone support available for Enterprise customers", {
        //      description: "Upgrade to Enterprise to access this feature.",
        //      duration: 4000,
        //    });
      },
    },
  ];

  const codeExample = `// JavaScript/Node.js Example
const response = await fetch('https://api.climateforte.com/climate/assess', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    latitude: 40.7128,
    longitude: -74.0060,
    industry: 'real_estate',
    timeHorizon: 30,
    options: {
      includeHistorical: true,
      includeProjections: true,
      detailedReport: true
    }
  })
});

const assessment = await response.json();

console.log('Overall Risk Score:', assessment.overallRisk);
console.log('Risk Categories:', assessment.categoryRisks);
console.log('Business Impact:', assessment.businessImpact);
console.log('Confidence Level:', assessment.confidence);`;

  // Event Handlers
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    //    toast.info(`Searching for: "${query}"`);
  };

  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  // Initialize notifications on mount
  //   useEffect(() => {
  //     setTimeout(() => {
  //       toast.success("Welcome to ClimateForte Help Center! How can we assist you?");
  //     }, 1000);
  //   }, []);

  // Scroll spy for active section
  useEffect(() => {
    const handleScroll = () => {
      const sections = helpCategories.map((cat) => cat.id);
      const scrollPosition = window.scrollY + 200;

      for (const sectionId of sections) {
        const element = document.getElementById(sectionId);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (
            scrollPosition >= offsetTop &&
            scrollPosition < offsetTop + offsetHeight
          ) {
            setActiveSection(sectionId);
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [helpCategories]);

  return (
    <>
      <Head>
        <title>Help & Support - ClimateForte</title>
        <meta
          name="description"
          content="Get help with ClimateForte's climate risk intelligence platform. Find answers, API docs, and expert support."
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        <link
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
          rel="stylesheet"
        />
      </Head>

      <div className="bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 min-h-screen">
        {/* <Navbar /> */}
        <NotificationSystem />

        {/* Hero Section */}
        <div className="max-w-7xl mx-auto px-6 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              How can we help you today?
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Find answers, learn about our platform, and get expert support for
              your climate risk intelligence needs.
            </p>

            <SearchBar
              onSearch={handleSearch}
              suggestions={searchSuggestions}
            />
          </motion.div>

          {/* Quick Help Categories */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
          >
            {helpCategories.slice(0, 4).map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
                onClick={() => scrollToSection(category.id)}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-6 cursor-pointer"
              >
                <div
                  className={`w-16 h-16 ${category.color.replace(
                    "text-",
                    "bg-"
                  )} rounded-2xl flex items-center justify-center mb-4 mx-auto`}
                >
                  <i className={`fas ${category.icon} text-white text-2xl`}></i>
                </div>
                <h3 className="text-xl font-bold text-white text-center mb-2">
                  {category.name}
                </h3>
                <p className="text-gray-300 text-center text-sm">
                  {category.description}
                </p>
                <div className="text-center mt-4">
                  <span className={`${category.color} text-sm font-medium`}>
                    {category.articleCount} articles
                  </span>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Contact Support Options */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-8 mb-12"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-white mb-4">
                Need Personalized Support?
              </h3>
              <p className="text-gray-300">
                Our expert team is here to help you succeed with climate risk
                intelligence.
              </p>
            </div>

            <SupportChannels channels={supportChannels} />
          </motion.div>
        </div>

        {/* Main Help Content */}
        <div className="max-w-7xl mx-auto px-6 pb-12">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar Navigation */}
            <div className="lg:col-span-1">
              <CategoryNavigation
                categories={helpCategories}
                activeSection={activeSection}
                onSectionClick={scrollToSection}
              />
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3 space-y-12">
              {/* Getting Started Section */}
              <motion.section
                id="getting-started"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-8"
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-[#10B981] rounded-xl flex items-center justify-center mr-4">
                    <i className="fas fa-rocket text-white"></i>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">
                      Getting Started
                    </h2>
                    <p className="text-gray-300">
                      Essential guides to help you begin your climate risk
                      assessment journey
                    </p>
                  </div>
                </div>

                <FAQSection faqs={faqs} />
              </motion.section>

              {/* API Documentation Section */}
              <motion.section
                id="api-docs"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-8"
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-[#0066CC] rounded-xl flex items-center justify-center mr-4">
                    <i className="fas fa-code text-white"></i>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">
                      API Documentation
                    </h2>
                    <p className="text-gray-300">
                      Integrate ClimateForte&#39;s climate intelligence into
                      your applications
                    </p>
                  </div>
                </div>

                {/* API Overview */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-white/5 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">
                      <i className="fas fa-bolt text-[#0066CC] mr-2"></i>Quick
                      Start
                    </h3>
                    <div className="space-y-3 text-sm text-gray-300">
                      <div>1. Get your API key from Settings</div>
                      <div>2. Make your first assessment call</div>
                      <div>3. Parse the JSON response</div>
                      <div>4. Integrate into your workflow</div>
                    </div>
                    <button className="mt-4 bg-[#0066CC] text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-all">
                      View Quick Start Guide
                    </button>
                  </div>

                  <div className="bg-white/5 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">
                      <i className="fas fa-book text-[#10B981] mr-2"></i>API
                      Reference
                    </h3>
                    <div className="space-y-2 text-sm text-gray-300">
                      <div>
                        <span className="text-[#10B981]">GET</span>{" "}
                        /climate/assess - Risk assessment
                      </div>
                      <div>
                        <span className="text-[#0066CC]">POST</span>{" "}
                        /portfolio/bulk - Bulk analysis
                      </div>
                      <div>
                        <span className="text-[#8B5CF6]">GET</span> /insights/ai
                        - AI predictions
                      </div>
                      <div>
                        <span className="text-[#F59E0B]">GET</span>{" "}
                        /weather/current - Live weather
                      </div>
                    </div>
                    <button className="mt-4 bg-[#10B981] text-white px-4 py-2 rounded-lg text-sm hover:bg-green-700 transition-all">
                      Full API Reference
                    </button>
                  </div>
                </div>

                <CodeExample code={codeExample} language="javascript" />

                {/* API Status */}
                <div className="bg-white/5 rounded-xl p-4 mt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative inline-block w-3 h-3 rounded-full bg-green-500 after:content-[''] after:absolute after:top-0 after:left-0 after:w-full after:h-full after:rounded-full"></div>
                      <span className="text-white font-medium">
                        API Status: All Systems Operational
                      </span>
                    </div>
                    <div className="text-sm text-gray-400">
                      Uptime: 99.97% | Response Time: 180ms
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Additional sections would follow the same pattern... */}
              {/* For brevity, I'm showing the structure for the remaining sections */}

              <motion.section
                id="features"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-[20px] border border-white/10 rounded-2xl p-8"
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-[#8B5CF6] rounded-xl flex items-center justify-center mr-4">
                    <i className="fas fa-tools text-white"></i>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">
                      Platform Features
                    </h2>
                    <p className="text-gray-300">
                      Master ClimateForte&#39;s powerful climate intelligence
                      capabilities
                    </p>
                  </div>
                </div>

                {/* Feature content here */}
                <div className="text-gray-300">
                  <p>Platform features content would be implemented here...</p>
                </div>
              </motion.section>

              {/* Continue with other sections... */}
            </div>
          </div>
        </div>

        <LiveChatWidget />
      </div>
    </>
  );
};

export default HelpPage;
