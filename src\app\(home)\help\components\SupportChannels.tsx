"use client";

import React from "react";
import { motion } from "framer-motion";
import { SupportChannel } from "./types";

const SupportChannels: React.FC<{ channels: SupportChannel[] }> = ({
  channels,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {channels.map((channel) => (
        <motion.div
          key={channel.id}
          whileHover={{ y: -4 }}
          className="bg-white/5 rounded-xl p-6 text-center cursor-pointer"
          onClick={channel.action}
        >
          <div
            className={`w-12 h-12 ${channel.color} rounded-xl flex items-center justify-center mx-auto mb-4`}
          >
            <i className={`fas ${channel.icon} text-white`}></i>
          </div>
          <h4 className="text-lg font-semibold text-white mb-2">
            {channel.name}
          </h4>
          <p className="text-gray-300 text-sm mb-4">{channel.description}</p>
          <div className="text-xs text-gray-400 mb-3">
            Response time: {channel.responseTime}
          </div>
          <button
            className={`${channel.color} text-white px-4 py-2 rounded-lg hover:opacity-80 transition-all text-sm font-medium`}
          >
            {channel.name === "Live Chat"
              ? "Start Chat"
              : channel.name === "Email Support"
              ? "Send Email"
              : "+1 (555) 123-4567"}
          </button>
        </motion.div>
      ))}
    </div>
  );
};

export default SupportChannels;
