"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";

const LiveChatWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const handleChatClick = () => {
    setIsOpen(!isOpen);
    // if (!isOpen) {
    // toast('Connecting to support agent...', {
    //   description: 'Please wait a moment.',
    //   duration: 2000,
    // });

    // setTimeout(() => {
    //   toast.success('Connected!', {
    //     description: 'A support agent will help you shortly.',
    //   });
    // }, 2000);
    // }
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        onClick={handleChatClick}
        className="bg-[#10B981] text-white p-4 rounded-full shadow-lg hover:bg-green-600 transition-all"
      >
        <i className="fas fa-comments text-xl"></i>
      </motion.button>
    </div>
  );
};

export default LiveChatWidget;
