"use client";

import React, { useState } from "react";
import { toast } from "sonner";

const CodeExample: React.FC<{ code: string; language: string }> = ({
  code,
  language,
}) => {
  const [copied, setCopied] = useState(false);
  console.warn(language);
  const copyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      toast.success("Code copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.warn(err);
      //   toast.error('Failed to copy code');
    }
  };

  return (
    <div className="bg-gray-900 rounded-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">
          Example: Climate Risk Assessment
        </h3>
        <button
          onClick={copyCode}
          className="text-[#0066CC] hover:text-[#14B8A6] transition-colors text-sm flex items-center space-x-2"
        >
          <i className={`fas ${copied ? "fa-check" : "fa-copy"}`}></i>
          <span>{copied ? "Copied!" : "Copy Code"}</span>
        </button>
      </div>
      <pre className="text-gray-300 text-sm overflow-x-auto">
        <code>{code}</code>
      </pre>
    </div>
  );
};

export default CodeExample;
